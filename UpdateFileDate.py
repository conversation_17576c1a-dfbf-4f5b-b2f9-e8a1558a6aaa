import os
import sys
import shutil
import ctypes
from datetime import datetime
from tkinter import Tk, Frame, Label, Entry, Button, Checkbutton, IntVar, Text, Scrollbar, END, DISABLED, NORMAL, BOTH, RIGHT, Y, LEFT, X, E, W
from tkinter import filedialog, messagebox

IMG_EXTS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tif', '.tiff', '.webp', '.heic', '.heif'}


def is_windows():
    return os.name == 'nt'


def ensure_dir(p: str):
    if not os.path.exists(p):
        os.makedirs(p, exist_ok=True)


def to_timestamp(dt: datetime) -> float:
    return dt.timestamp()


def set_file_times_cross_platform(path: str, dt: datetime):
    ts = to_timestamp(dt)
    # Set access and modified time
    os.utime(path, (ts, ts))
    # Try to set creation time on Windows
    if is_windows():
        try:
            set_windows_creation_time(path, dt)
        except Exception:
            pass


def set_windows_creation_time(path: str, dt: datetime):
    # Use WinAPI SetFileTime to set creation time
    FILE_WRITE_ATTRIBUTES = 0x0100
    OPEN_EXISTING = 3
    FILE_SHARE_READ = 1
    FILE_SHARE_WRITE = 2
    FILE_SHARE_DELETE = 4

    CreateFileW = ctypes.windll.kernel32.CreateFileW
    SetFileTime = ctypes.windll.kernel32.SetFileTime
    CloseHandle = ctypes.windll.kernel32.CloseHandle

    handle = CreateFileW(path, FILE_WRITE_ATTRIBUTES, FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
                         None, OPEN_EXISTING, 0, None)
    if handle == -1 or handle == 0:
        raise OSError('Failed to open file for setting time')

    # Convert Python datetime to Windows FILETIME (100-ns intervals since Jan 1, 1601)
    # First convert to UTC
    if dt.tzinfo is None:
        # Assume local time; convert to UTC by accounting for local offset
        # Use timestamp (seconds since epoch) which is UTC-based
        epoch_seconds = dt.timestamp()
    else:
        epoch_seconds = dt.timestamp()

    # Convert seconds since 1970 to 100-ns intervals since 1601
    WINDOWS_TICK = ********
    SEC_TO_UNIX_EPOCH = ***********
    filetime = int((epoch_seconds + SEC_TO_UNIX_EPOCH) * WINDOWS_TICK)

    c_time = ctypes.c_ulonglong(filetime)
    a_time = ctypes.c_ulonglong(filetime)
    m_time = ctypes.c_ulonglong(filetime)

    c_ptr = ctypes.byref(ctypes.c_ulonglong(c_time.value))
    a_ptr = ctypes.byref(ctypes.c_ulonglong(a_time.value))
    m_ptr = ctypes.byref(ctypes.c_ulonglong(m_time.value))

    ok = SetFileTime(handle, c_ptr, a_ptr, m_ptr)
    CloseHandle(handle)
    if ok == 0:
        raise OSError('SetFileTime failed')


def parse_datetime(text: str, use_now: bool) -> datetime:
    if use_now or not text.strip():
        return datetime.now()
    # Accept formats like: YYYY-MM-DD HH:MM:SS or YYYY/MM/DD HH:MM:SS or date only
    text = text.strip()
    fmts = [
        '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S',
        '%Y-%m-%d %H:%M', '%Y/%m/%d %H:%M',
        '%Y-%m-%d', '%Y/%m/%d'
    ]
    for f in fmts:
        try:
            return datetime.strptime(text, f)
        except ValueError:
            continue
    raise ValueError('无法解析日期时间，请使用格式：YYYY-MM-DD HH:MM:SS')


def iter_image_files(root: str, include_sub: bool):
    if include_sub:
        for dirpath, _, filenames in os.walk(root):
            for name in filenames:
                ext = os.path.splitext(name)[1].lower()
                if ext in IMG_EXTS:
                    yield os.path.join(dirpath, name)
    else:
        for name in os.listdir(root):
            p = os.path.join(root, name)
            if os.path.isfile(p) and os.path.splitext(name)[1].lower() in IMG_EXTS:
                yield p


class App:
    def __init__(self, master: Tk):
        self.master = master
        master.title('图片文件日期修改工具')
        master.geometry('760x520')

        self.src_dir = ''
        self.out_dir = ''

        self.include_sub = IntVar(value=1)
        self.use_now = IntVar(value=1)

        top = Frame(master)
        top.pack(fill=X, padx=10, pady=10)

        # Source
        Label(top, text='源图片文件夹:').grid(row=0, column=0, sticky=E)
        self.src_entry = Entry(top, width=70)
        self.src_entry.grid(row=0, column=1, padx=5)
        Button(top, text='选择...', command=self.choose_src).grid(row=0, column=2)

        # Output
        Label(top, text='输出文件夹(可选):').grid(row=1, column=0, sticky=E)
        self.out_entry = Entry(top, width=70)
        self.out_entry.grid(row=1, column=1, padx=5)
        Button(top, text='选择...', command=self.choose_out).grid(row=1, column=2)

        # Options
        self.sub_cb = Checkbutton(top, text='包含子文件夹', variable=self.include_sub)
        self.sub_cb.grid(row=2, column=1, sticky=W)

        Label(top, text='目标日期时间:').grid(row=3, column=0, sticky=E)
        self.dt_entry = Entry(top, width=30)
        self.dt_entry.insert(0, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        self.dt_entry.grid(row=3, column=1, sticky=W, padx=5)
        self.now_cb = Checkbutton(top, text='使用当前时间', variable=self.use_now, command=self.toggle_dt_entry)
        self.now_cb.grid(row=3, column=1, sticky=E)

        # Buttons
        btns = Frame(master)
        btns.pack(fill=X, padx=10)
        self.btn_update_copy = Button(btns, text='更新到输出(复制并修改日期)', command=self.update_to_output)
        self.btn_update_copy.pack(side=LEFT, padx=5, pady=5)
        self.btn_update_inplace = Button(btns, text='直接修改(原地修改日期)', command=self.update_in_place)
        self.btn_update_inplace.pack(side=LEFT, padx=5, pady=5)

        # Log area
        logf = Frame(master)
        logf.pack(fill=BOTH, expand=True, padx=10, pady=10)
        Label(logf, text='日志:').pack(anchor=W)
        self.log = Text(logf, height=18)
        sb = Scrollbar(logf, command=self.log.yview)
        self.log.configure(yscrollcommand=sb.set)
        self.log.pack(side=LEFT, fill=BOTH, expand=True)
        sb.pack(side=RIGHT, fill=Y)

        self.toggle_dt_entry()

    def choose_src(self):
        d = filedialog.askdirectory(title='选择源图片文件夹')
        if d:
            self.src_dir = d
            self.src_entry.delete(0, END)
            self.src_entry.insert(0, d)

    def choose_out(self):
        d = filedialog.askdirectory(title='选择输出文件夹')
        if d:
            self.out_dir = d
            self.out_entry.delete(0, END)
            self.out_entry.insert(0, d)

    def toggle_dt_entry(self):
        if self.use_now.get() == 1:
            self.dt_entry.configure(state=DISABLED)
        else:
            self.dt_entry.configure(state=NORMAL)

    def gather_dt(self) -> datetime:
        try:
            return parse_datetime(self.dt_entry.get(), self.use_now.get() == 1)
        except Exception as e:
            messagebox.showerror('错误', str(e))
            raise

    def logln(self, msg: str):
        self.log.insert(END, msg + '\n')
        self.log.see(END)
        self.master.update_idletasks()

    def update_to_output(self):
        src = self.src_entry.get().strip()
        out = self.out_entry.get().strip()
        if not src:
            messagebox.showwarning('提示', '请先选择源图片文件夹')
            return
        if not out:
            messagebox.showwarning('提示', '请先选择输出文件夹')
            return
        if os.path.abspath(src) == os.path.abspath(out):
            messagebox.showwarning('提示', '输出文件夹不能与源文件夹相同')
            return
        try:
            dt = self.gather_dt()
        except Exception:
            return
        include_sub = self.include_sub.get() == 1
        self.process_copy(src, out, dt, include_sub)

    def update_in_place(self):
        src = self.src_entry.get().strip()
        if not src:
            messagebox.showwarning('提示', '请先选择存储图片的文件夹')
            return
        try:
            dt = self.gather_dt()
        except Exception:
            return
        include_sub = self.include_sub.get() == 1
        self.process_inplace(src, dt, include_sub)

    def process_copy(self, src: str, out: str, dt: datetime, include_sub: bool):
        files = list(iter_image_files(src, include_sub))
        if not files:
            self.logln('未找到图片文件。')
            return
        self.logln(f'找到 {len(files)} 个图片文件，开始复制到输出并修改日期...')
        count_ok = 0
        base = os.path.abspath(src)
        for i, f in enumerate(files, 1):
            try:
                rel = os.path.relpath(f, base)
                dest = os.path.join(out, rel)
                ensure_dir(os.path.dirname(dest))
                shutil.copy2(f, dest)
                set_file_times_cross_platform(dest, dt)
                count_ok += 1
                self.logln(f'[{i}/{len(files)}] 已处理: {dest}')
            except Exception as e:
                self.logln(f'[{i}/{len(files)}] 失败: {f} -> {e}')
        self.logln(f'完成：成功 {count_ok}/{len(files)}')

    def process_inplace(self, src: str, dt: datetime, include_sub: bool):
        files = list(iter_image_files(src, include_sub))
        if not files:
            self.logln('未找到图片文件。')
            return
        self.logln(f'找到 {len(files)} 个图片文件，开始原地修改日期...')
        count_ok = 0
        for i, f in enumerate(files, 1):
            try:
                set_file_times_cross_platform(f, dt)
                count_ok += 1
                self.logln(f'[{i}/{len(files)}] 已修改: {f}')
            except Exception as e:
                self.logln(f'[{i}/{len(files)}] 失败: {f} -> {e}')
        self.logln(f'完成：成功 {count_ok}/{len(files)}')


def main():
    root = Tk()
    App(root)
    root.mainloop()


if __name__ == '__main__':
    main()
