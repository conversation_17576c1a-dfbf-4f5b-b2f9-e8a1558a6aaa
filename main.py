import sys
import time
from pathlib import Path

# 需求：
# 1) 打开 Edge 浏览器（指定路径）
# 2) 打开 https://www.doubao.com
# 3) 等待 3 秒
# 4) 点击给定 XPath 的 textarea
# 5) 再等待 1 秒后输入“你好”

EDGE_EXE = r"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe"  # 可用 --edge 覆盖
TARGET_URL = "https://www.doubao.com"
XPATH_TEXTAREA = \
    "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[2]/div/div/div[2]/div[2]/div[2]/div[2]/div[2]/div[1]/div/textarea"
DEFAULT_TEXT = "你好"
# 输入后再点击的目标 XPath（你提供的 svg/path 节点）
XPATH_CLICK_AFTER = \
    "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[2]/div[1]/div/div[2]/div[2]/div[2]/div[2]/div[2]/div[2]/div[3]/div/div[3]/div[2]/button"

# 新增：按你的步骤所需的 XPath 和号码
XPATH_STEP1 = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[1]/div/div/div/div[2]/div/button[2]/span"
XPATH_INPUT = "/html/body/div[5]/div/div[2]/div/div/div/div/div/div/div/div[2]/div[2]/div[1]/div/div[2]/input"
XPATH_STEP3 = "/html/body/div[5]/div/div[2]/div/div/div/div/div/div/div/div[2]/div[2]/div[2]/span/span/span"
XPATH_STEP4 = "/html/body/div[5]/div/div[2]/div/div/div/div/div/div/div/div[2]/div[2]/div[1]/button/span"
PHONE_NUMBER = "19942211213"



def open_edge_and_type(edge_path: str, url: str, xpath: str, text: str) -> int:
    try:
        from playwright.sync_api import sync_playwright
    except Exception:
        print("缺少依赖：playwright。请先安装：\n  pip install playwright\n  playwright install")
        return 4

    def find_locator_any_frame(page, xp: str):
        loc = page.locator(f"xpath={xp}")
        if loc.count():
            return loc
        for fr in page.frames:
            try:
                loc2 = fr.locator(f"xpath={xp}")
                if loc2.count():
                    return loc2
            except Exception:
                continue
        return page.locator(f"xpath={xp}")

    with sync_playwright() as p:
        browser = None
        # 优先使用指定的 Edge 可执行文件；失败则回退到 channel='msedge'
        try:
            if Path(edge_path).is_file():
                browser = p.chromium.launch(executable_path=edge_path, headless=False)
            else:
                browser = p.chromium.launch(channel="msedge", headless=False)
        except Exception as e:
            print(f"使用指定路径启动 Edge 失败：{e}，尝试使用 channel='msedge'…")
            browser = p.chromium.launch(channel="msedge", headless=False)

        page = browser.new_page()
        page.goto(url, wait_until="domcontentloaded")

        # 3) 等待 3 秒
        time.sleep(3)

        # 4) 点击 XPath 指向的 textarea
        loc = find_locator_any_frame(page, xpath)
        try:
            loc.wait_for(state="visible", timeout=20000)
        except Exception:
            pass
        try:
            loc.scroll_into_view_if_needed()
            loc.click(force=True)
        except Exception as e:
            print(f"直接点击失败：{e}，尝试使用 JS 聚焦……")
            page.evaluate(
                "(xp)=>{const n=document.evaluate(xp,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue; if(n){n.focus();}}",
                xpath,
            )

        # 5) 1 秒后输入文本（分多种方式兜底）
        time.sleep(1)
        # 方式 A：fill（对 textarea 最稳）
        filled = False
        try:
            loc.fill(text)
            filled = True
        except Exception:
            pass
        # 方式 B：type（模拟真实键入）
        if not filled:
            try:
                loc.type(text, delay=50)
                filled = True
            except Exception:
                pass
        # 方式 C：键盘直接输入（前提是已经 focus）
        if not filled:
            try:
                page.keyboard.type(text, delay=50)
                filled = True
            except Exception:
                pass
        # 方式 D：JS 直接赋值并触发事件
        if not filled:
            try:
                page.evaluate(
                    """
                    (xp, txt)=>{
                      const el=document.evaluate(xp,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;
                      if(!el) return;
                      if('value' in el){
                        el.focus(); el.value=''; el.dispatchEvent(new Event('input',{bubbles:true}));
                        el.value=txt; el.dispatchEvent(new Event('input',{bubbles:true}));
                      } else if (el.getAttribute && el.getAttribute('contenteditable')==='true'){
                        el.focus(); document.execCommand('selectAll', false, null);
                        document.execCommand('insertText', false, txt);
                      }
                    }
                    """,
                    xpath,
                    text,
                )
                filled = True
            except Exception as e:
                print(f"JS 赋值仍失败：{e}")

        # 输入完成后等待 1 秒，按你的 XPath 点击目标按钮（svg/path）
        time.sleep(1)
        click_loc = find_locator_any_frame(page, XPATH_CLICK_AFTER)
        try:
            click_loc.scroll_into_view_if_needed()
            # svg/path 不能直接点击时，对应父级 button 再点击
            try:
                click_loc.click(force=True)
            except Exception:
                parent_btn = click_loc.locator("xpath=ancestor::button[1]")
                if parent_btn.count():
                    parent_btn.click(force=True)
                else:
                    click_loc.evaluate("(n)=>n.parentElement && n.parentElement.click()")
        except Exception as e:
            print(f"点击发送按钮失败：{e}")

        # 不自动退出：保持进程常驻，浏览器不会被自动关闭
        print("已完成输入与点击。保持浏览器运行，按 Ctrl+C 结束脚本…")
        try:
            while True:
                time.sleep(3600)
        except KeyboardInterrupt:
            return 0


def open_edge_and_run_sequence(edge_path: str, url: str) -> int:
    """按照用户给定的 XPath 顺序执行点击/输入动作：
    1) 点击 XPATH_STEP1
    2) 点击输入框 XPATH_INPUT 并输入 PHONE_NUMBER
    3) 点击 XPATH_STEP3
    4) 点击 XPATH_STEP4
    步骤之间停留 1 秒，最后停留 30 秒后退出。
    """
    try:
        from playwright.sync_api import sync_playwright
    except Exception:
        print("缺少依赖：playwright。请先安装：\n  pip install playwright\n  playwright install")
        return 4

    def find_locator_any_frame(page, xp: str):
        loc = page.locator(f"xpath={xp}")
        if loc.count():
            return loc
        for fr in page.frames:
            try:
                loc2 = fr.locator(f"xpath={xp}")
                if loc2.count():
                    return loc2
            except Exception:
                continue
        return page.locator(f"xpath={xp}")

    def safe_click(page, xp: str):
        loc = find_locator_any_frame(page, xp)
        try:
            loc.wait_for(state="visible", timeout=20000)
        except Exception:
            pass
        try:
            loc.scroll_into_view_if_needed()
            loc.click(force=True)
        except Exception:
            # 兜底：尝试点击最近的父级 button
            try:
                parent_btn = loc.locator("xpath=ancestor::button[1]")
                if parent_btn.count():
                    parent_btn.click(force=True)
                else:
                    loc.evaluate("(n)=>n && n.parentElement && n.parentElement.click()")
            except Exception as e:
                print(f"点击失败（{xp}）：{e}")

    with sync_playwright() as p:
        browser = None
        try:
            if Path(edge_path).is_file():
                browser = p.chromium.launch(executable_path=edge_path, headless=False)
            else:
                browser = p.chromium.launch(channel="msedge", headless=False)
        except Exception as e:
            print(f"使用指定路径启动 Edge 失败：{e}，尝试使用 channel='msedge'…")
            browser = p.chromium.launch(channel="msedge", headless=False)

        page = browser.new_page()
        page.goto(url, wait_until="domcontentloaded")

        # Step 1
        safe_click(page, XPATH_STEP1)
        time.sleep(1)

        # Step 2: 输入手机号
        input_loc = find_locator_any_frame(page, XPATH_INPUT)
        try:
            input_loc.wait_for(state="visible", timeout=20000)
        except Exception:
            pass
        try:
            input_loc.scroll_into_view_if_needed()
            input_loc.click(force=True)
        except Exception as e:
            print(f"点击输入框失败：{e}")

        filled = False
        try:
            input_loc.fill(PHONE_NUMBER)
            filled = True
        except Exception:
            pass
        if not filled:
            try:
                input_loc.type(PHONE_NUMBER, delay=50)
                filled = True
            except Exception:
                pass
        if not filled:
            try:
                page.keyboard.type(PHONE_NUMBER, delay=50)
                filled = True
            except Exception:
                pass
        time.sleep(1)

        # Step 3
        safe_click(page, XPATH_STEP3)
        time.sleep(1)

        # Step 4
        safe_click(page, XPATH_STEP4)


        # 最后停留 30 秒
        time.sleep(30)
        return 0

def main(argv: list[str]) -> int:
    edge_path = EDGE_EXE
    url = TARGET_URL
    # 保留原有 --text 参数但本次新流程不使用
    text = DEFAULT_TEXT

    # 参数：--edge <path>  --url <url>  --text <text>
    if "--edge" in argv:
        try:
            edge_path = argv[argv.index("--edge") + 1]
        except Exception:
            print("--edge 需要紧随 Edge 可执行文件路径")
            return 3
    if "--url" in argv:
        try:
            url = argv[argv.index("--url") + 1]
        except Exception:
            pass
    if "--text" in argv:
        try:
            text = argv[argv.index("--text") + 1]
        except Exception:
            pass

    # 改为运行你的新步骤序列
    return open_edge_and_run_sequence(edge_path, url)


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
