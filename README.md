# 豆包AI海报生成自动化脚本

这是一个用于自动化操作豆包AI生成海报的Python脚本。

## 安装要求

1. **Python 3.7+**
2. **Microsoft Edge浏览器**
3. **Edge WebDriver** (会自动下载)

## 安装步骤

1. 安装依赖包：
```bash
pip install -r requirements.txt
```

2. 运行脚本：
```bash
python doubao_poster_automation.py
```

## 功能说明

### 当前已实现功能：
- ✅ 自动启动Edge浏览器
- ✅ 自动打开豆包AI网站 (www.doubao.com)
- ✅ 等待用户指示下一步操作

### 待实现功能：
- ⏳ 根据用户需求添加更多自动化操作

## 使用说明

1. 运行脚本后，程序会自动：
   - 启动Edge浏览器
   - 打开豆包AI网站
   - 显示当前页面信息

2. 完成第一步后，程序会暂停等待用户输入下一步操作指令

## 注意事项

- 确保系统已安装Microsoft Edge浏览器
- 首次运行时，Selenium会自动下载对应版本的Edge WebDriver
- 如果遇到驱动问题，请检查Edge浏览器版本是否为最新版本

## 故障排除

如果遇到问题，请检查：
1. Edge浏览器是否已安装且为最新版本
2. 网络连接是否正常
3. 防火墙是否阻止了浏览器访问
