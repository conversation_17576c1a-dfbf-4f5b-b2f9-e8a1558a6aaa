"""
豆包AI海报生成自动化脚本
结合webbrowser和selenium实现自动化操作
"""

import webbrowser
import time
import os
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException


class DoubaoSimpleAutomation:
    def __init__(self):
        """初始化自动化类"""
        self.url = "https://www.doubao.com"
        self.driver = None
        self.wait = None
        self.image_generate_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[2]/div/div/div[2]/div[3]/div/div/div/div/div[2]/div[1]/div"

    def setup_selenium_driver(self):
        """设置Selenium驱动（用于点击操作）"""
        try:
            print("正在设置Selenium驱动...")
            # 配置Edge浏览器选项
            edge_options = Options()
            edge_options.add_argument("--disable-blink-features=AutomationControlled")
            edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            edge_options.add_experimental_option('useAutomationExtension', False)

            # 尝试多种方式创建Edge驱动实例
            driver_paths = [
                "./drivers/msedgedriver.exe",  # 本地下载的驱动
                "msedgedriver.exe",  # 系统PATH中的驱动
                None  # 让Selenium自动查找
            ]

            for driver_path in driver_paths:
                try:
                    if driver_path and os.path.exists(driver_path):
                        print(f"尝试使用本地驱动: {driver_path}")
                        service = Service(driver_path)
                        self.driver = webdriver.Edge(service=service, options=edge_options)
                    else:
                        print("尝试使用系统默认驱动...")
                        self.driver = webdriver.Edge(options=edge_options)

                    self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                    self.wait = WebDriverWait(self.driver, 10)
                    self.driver.maximize_window()
                    print("✅ Selenium驱动设置成功！")
                    return True

                except Exception as e:
                    print(f"驱动路径 {driver_path} 失败: {e}")
                    continue

            print("❌ 所有驱动路径都失败了")
            return False

        except Exception as e:
            print(f"设置Selenium驱动时发生错误: {e}")
            return False

    def open_doubao_website(self):
        """使用默认浏览器打开豆包AI网站"""
        try:
            print("正在打开豆包AI网站...")
            print(f"目标网址: {self.url}")
            
            # 使用默认浏览器打开网站
            webbrowser.open(self.url)
            
            print("豆包AI网站已在默认浏览器中打开！")
            print("请在浏览器中查看网站是否正确加载")
            
            return True
            
        except Exception as e:
            print(f"打开豆包AI网站失败: {e}")
            return False
    
    def open_edge_browser(self):
        """尝试使用Edge浏览器打开网站"""
        try:
            print("正在尝试使用Edge浏览器打开豆包AI网站...")
            
            # Windows系统中Edge浏览器的常见路径
            edge_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
            ]
            
            edge_path = None
            for path in edge_paths:
                if os.path.exists(path):
                    edge_path = path
                    break
            
            if edge_path:
                print(f"找到Edge浏览器: {edge_path}")
                # 注册Edge浏览器
                webbrowser.register('edge', None, webbrowser.BackgroundBrowser(edge_path))
                # 使用Edge打开网站
                webbrowser.get('edge').open(self.url)
                print("豆包AI网站已在Edge浏览器中打开！")
                return True
            else:
                print("未找到Edge浏览器，使用默认浏览器...")
                return self.open_doubao_website()
                
        except Exception as e:
            print(f"使用Edge浏览器失败: {e}")
            print("尝试使用默认浏览器...")
            return self.open_doubao_website()

    def open_with_selenium(self):
        """使用Selenium打开豆包AI网站并执行自动化操作"""
        try:
            if not self.driver:
                print("Selenium驱动未初始化")
                return False

            print("正在使用Selenium打开豆包AI网站...")
            self.driver.get(self.url)

            print("网站已打开，等待5秒...")
            time.sleep(10)

            print("正在查找图像生成按钮...")
            try:
                # 等待元素可点击
                image_generate_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, self.image_generate_xpath))
                )

                print("找到图像生成按钮，正在点击...")
                image_generate_button.click()

                print("✅ 成功点击图像生成按钮！")
                time.sleep(2)  # 等待页面响应

                return True

            except TimeoutException:
                print("❌ 未找到图像生成按钮（超时）")
                print("可能页面结构已改变或加载时间过长")
                return False
            except Exception as e:
                print(f"❌ 点击图像生成按钮时发生错误: {e}")
                return False

        except Exception as e:
            print(f"使用Selenium操作时发生错误: {e}")
            return False

    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")

    def wait_for_user_input(self):
        """等待用户输入下一步操作"""
        print("\n" + "="*50)
        print("第一步完成！浏览器已打开豆包AI网站")
        print("请在浏览器中确认网站已正确加载")
        print("然后告诉我接下来需要执行的操作...")
        print("="*50)
        
        # 等待用户指示
        input("按Enter键继续或Ctrl+C退出...")


def main():
    """主函数"""
    print("开始启动豆包AI自动化脚本...")
    automation = DoubaoSimpleAutomation()

    try:
        # 设置Selenium驱动
        if automation.setup_selenium_driver():
            print("使用Selenium模式进行自动化操作...")

            # 使用Selenium打开网站并执行自动化操作
            if automation.open_with_selenium():
                print("\n" + "="*50)
                print("✅ 自动化操作完成！")
                print("- 已打开豆包AI网站")
                print("- 已等待5秒")
                print("- 已点击图像生成按钮")
                print("="*50)
            else:
                print("❌ 自动化操作失败")
        else:
            print("Selenium驱动设置失败，尝试使用简单模式...")
            # 回退到简单模式
            if automation.open_edge_browser():
                print("请手动在浏览器中点击图像生成按钮")
            else:
                print("所有浏览器打开尝试都失败了")
                return

        # 等待用户输入下一步操作
        automation.wait_for_user_input()

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        automation.close_browser()


if __name__ == "__main__":
    main()
