"""
豆包AI海报生成自动化脚本
结合webbrowser和selenium实现自动化操作
"""

import webbrowser
import time
import os
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException


class DoubaoSimpleAutomation:
    def __init__(self):
        """初始化自动化类"""
        self.url = "https://www.doubao.com"
        self.driver = None
        self.wait = None

        # 多种图像生成按钮的定位策略
        self.image_generate_selectors = [
            # 基于文本内容的定位
            "//div[contains(text(), '图像生成') or contains(text(), '图片生成') or contains(text(), '画图')]",
            "//button[contains(text(), '图像生成') or contains(text(), '图片生成') or contains(text(), '画图')]",
            "//span[contains(text(), '图像生成') or contains(text(), '图片生成') or contains(text(), '画图')]",

            # 基于class名称的定位
            "//div[contains(@class, 'image') and contains(@class, 'generate')]",
            "//div[contains(@class, 'skill-bar-item')]//div[contains(@class, 'skill-bar-button')]",

            # 基于data属性的定位
            "//div[@data-testid='skill_bar_button_3']",
            "//div[contains(@data-testid, 'image') or contains(@data-testid, 'generate')]",

            # 基于图标的定位
            "//div[contains(@class, 'icon') and contains(@class, 'image')]",
            "//svg[contains(@class, 'icon')]/../..",

            # 更通用的定位策略
            "//div[@role='button' and contains(., '图')]",
            "//div[contains(@class, 'button') and contains(., '图')]",

            # 原始的XPath作为备选
            "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[2]/div/div/div[2]/div[3]/div/div/div/div/div[2]/div[1]/div"
        ]

    def setup_selenium_driver(self):
        """设置Selenium驱动（用于点击操作）"""
        try:
            print("正在设置Selenium驱动...")
            # 配置Edge浏览器选项
            edge_options = Options()
            edge_options.add_argument("--disable-blink-features=AutomationControlled")
            edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            edge_options.add_experimental_option('useAutomationExtension', False)

            # 尝试多种方式创建Edge驱动实例
            driver_paths = [
                "./drivers/msedgedriver.exe",  # 本地下载的驱动
                "msedgedriver.exe",  # 系统PATH中的驱动
                None  # 让Selenium自动查找
            ]

            for driver_path in driver_paths:
                try:
                    if driver_path and os.path.exists(driver_path):
                        print(f"尝试使用本地驱动: {driver_path}")
                        service = Service(driver_path)
                        self.driver = webdriver.Edge(service=service, options=edge_options)
                    else:
                        print("尝试使用系统默认驱动...")
                        self.driver = webdriver.Edge(options=edge_options)

                    self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                    self.wait = WebDriverWait(self.driver, 20)  # 增加等待时间到20秒
                    self.driver.maximize_window()
                    print("✅ Selenium驱动设置成功！")
                    return True

                except Exception as e:
                    print(f"驱动路径 {driver_path} 失败: {e}")
                    continue

            print("❌ 所有驱动路径都失败了")
            return False

        except Exception as e:
            print(f"设置Selenium驱动时发生错误: {e}")
            return False

    def open_doubao_website(self):
        """使用默认浏览器打开豆包AI网站"""
        try:
            print("正在打开豆包AI网站...")
            print(f"目标网址: {self.url}")
            
            # 使用默认浏览器打开网站
            webbrowser.open(self.url)
            
            print("豆包AI网站已在默认浏览器中打开！")
            print("请在浏览器中查看网站是否正确加载")
            
            return True
            
        except Exception as e:
            print(f"打开豆包AI网站失败: {e}")
            return False
    
    def open_edge_browser(self):
        """尝试使用Edge浏览器打开网站"""
        try:
            print("正在尝试使用Edge浏览器打开豆包AI网站...")
            
            # Windows系统中Edge浏览器的常见路径
            edge_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
            ]
            
            edge_path = None
            for path in edge_paths:
                if os.path.exists(path):
                    edge_path = path
                    break
            
            if edge_path:
                print(f"找到Edge浏览器: {edge_path}")
                # 注册Edge浏览器
                webbrowser.register('edge', None, webbrowser.BackgroundBrowser(edge_path))
                # 使用Edge打开网站
                webbrowser.get('edge').open(self.url)
                print("豆包AI网站已在Edge浏览器中打开！")
                return True
            else:
                print("未找到Edge浏览器，使用默认浏览器...")
                return self.open_doubao_website()
                
        except Exception as e:
            print(f"使用Edge浏览器失败: {e}")
            print("尝试使用默认浏览器...")
            return self.open_doubao_website()

    def open_with_selenium(self):
        """使用Selenium打开豆包AI网站并执行自动化操作"""
        try:
            if not self.driver:
                print("Selenium驱动未初始化")
                return False

            print("正在使用Selenium打开豆包AI网站...")
            self.driver.get(self.url)

            print("网站已打开，等待5秒...")
            time.sleep(10)

            return self.find_and_click_image_button()

        except Exception as e:
            print(f"使用Selenium操作时发生错误: {e}")
            return False

    def find_and_click_image_button(self):
        """使用多种策略查找并点击图像生成按钮"""
        print("正在查找图像生成按钮...")

        # 首先等待页面完全加载
        print("等待页面完全加载...")
        time.sleep(5)

        # 尝试滚动到页面中部，确保按钮可见
        try:
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(2)
        except:
            pass

        # 尝试多种定位策略
        for i, selector in enumerate(self.image_generate_selectors, 1):
            try:
                print(f"尝试定位策略 {i}/{len(self.image_generate_selectors)}: {selector[:50]}...")

                # 等待元素出现
                element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )

                # 滚动到元素位置
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(1)

                # 等待元素可点击
                clickable_element = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )

                print(f"✅ 找到图像生成按钮！使用策略 {i}")
                print(f"元素文本: {element.text}")
                print(f"元素标签: {element.tag_name}")

                # 尝试点击
                try:
                    clickable_element.click()
                    print("✅ 成功点击图像生成按钮！")
                    time.sleep(3)  # 等待页面响应
                    return True
                except Exception as click_error:
                    print(f"直接点击失败，尝试JavaScript点击: {click_error}")
                    # 使用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", clickable_element)
                    print("✅ 使用JavaScript成功点击图像生成按钮！")
                    time.sleep(3)
                    return True

            except TimeoutException:
                print(f"策略 {i} 超时，尝试下一个...")
                continue
            except Exception as e:
                print(f"策略 {i} 失败: {e}")
                continue

        # 如果所有策略都失败，尝试查找所有可能的按钮
        print("所有预定义策略都失败，尝试查找所有可能的按钮...")
        return self.find_buttons_by_inspection()

    def find_buttons_by_inspection(self):
        """通过检查页面上的所有按钮来查找图像生成按钮"""
        try:
            # 查找所有可能的按钮元素
            button_selectors = [
                "//div[@role='button']",
                "//button",
                "//div[contains(@class, 'button')]",
                "//div[contains(@class, 'btn')]",
                "//div[@onclick]",
                "//div[contains(@class, 'clickable')]"
            ]

            for selector in button_selectors:
                try:
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    print(f"找到 {len(buttons)} 个按钮元素 (选择器: {selector})")

                    for button in buttons:
                        try:
                            button_text = button.text.strip()
                            button_class = button.get_attribute('class') or ''
                            button_data_testid = button.get_attribute('data-testid') or ''

                            # 检查是否包含图像相关的关键词
                            keywords = ['图像', '图片', '画图', 'image', 'generate', 'draw', 'paint']
                            if any(keyword in button_text.lower() or
                                  keyword in button_class.lower() or
                                  keyword in button_data_testid.lower()
                                  for keyword in keywords):

                                print(f"找到可能的图像生成按钮:")
                                print(f"  文本: {button_text}")
                                print(f"  类名: {button_class}")
                                print(f"  data-testid: {button_data_testid}")

                                # 尝试点击
                                try:
                                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                                    time.sleep(1)
                                    button.click()
                                    print("✅ 成功点击可能的图像生成按钮！")
                                    time.sleep(3)
                                    return True
                                except:
                                    try:
                                        self.driver.execute_script("arguments[0].click();", button)
                                        print("✅ 使用JavaScript成功点击可能的图像生成按钮！")
                                        time.sleep(3)
                                        return True
                                    except Exception as e:
                                        print(f"点击失败: {e}")
                                        continue
                        except Exception as e:
                            continue

                except Exception as e:
                    continue

            print("❌ 未找到图像生成按钮")
            print("请检查页面是否正确加载，或者页面结构是否发生变化")
            return False

        except Exception as e:
            print(f"检查按钮时发生错误: {e}")
            return False

        except Exception as e:
            print(f"使用Selenium操作时发生错误: {e}")
            return False

    def debug_page_structure(self):
        """调试页面结构，输出所有可能的按钮信息"""
        try:
            print("\n" + "="*60)
            print("调试模式：分析页面结构")
            print("="*60)

            # 获取页面标题
            print(f"页面标题: {self.driver.title}")
            print(f"当前URL: {self.driver.current_url}")

            # 查找所有包含"技能"、"按钮"等关键词的元素
            debug_selectors = [
                "//div[contains(@class, 'skill')]",
                "//div[contains(@class, 'bar')]",
                "//div[contains(@class, 'button')]",
                "//div[@data-testid]",
                "//div[@role='button']"
            ]

            for selector in debug_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"\n找到 {len(elements)} 个元素 (选择器: {selector}):")
                        for i, elem in enumerate(elements[:5]):  # 只显示前5个
                            try:
                                text = elem.text.strip()[:50]
                                class_name = elem.get_attribute('class') or ''
                                data_testid = elem.get_attribute('data-testid') or ''
                                print(f"  {i+1}. 文本: '{text}' | 类名: '{class_name[:30]}' | data-testid: '{data_testid}'")
                            except:
                                continue
                except:
                    continue

            print("="*60)

        except Exception as e:
            print(f"调试页面结构时发生错误: {e}")

    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")

    def wait_for_user_input(self):
        """等待用户输入下一步操作"""
        print("\n" + "="*50)
        print("第一步完成！浏览器已打开豆包AI网站")
        print("请在浏览器中确认网站已正确加载")
        print("然后告诉我接下来需要执行的操作...")
        print("="*50)
        
        # 等待用户指示
        input("按Enter键继续或Ctrl+C退出...")


def main():
    """主函数"""
    print("开始启动豆包AI自动化脚本...")
    automation = DoubaoSimpleAutomation()

    try:
        # 设置Selenium驱动
        if automation.setup_selenium_driver():
            print("使用Selenium模式进行自动化操作...")

            # 使用Selenium打开网站并执行自动化操作
            if automation.open_with_selenium():
                print("\n" + "="*50)
                print("✅ 自动化操作完成！")
                print("- 已打开豆包AI网站")
                print("- 已等待5秒")
                print("- 已点击图像生成按钮")
                print("="*50)
            else:
                print("❌ 自动化操作失败")
        else:
            print("Selenium驱动设置失败，尝试使用简单模式...")
            # 回退到简单模式
            if automation.open_edge_browser():
                print("请手动在浏览器中点击图像生成按钮")
            else:
                print("所有浏览器打开尝试都失败了")
                return

        # 等待用户输入下一步操作
        automation.wait_for_user_input()

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        automation.close_browser()


if __name__ == "__main__":
    main()
